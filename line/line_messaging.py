import requests
from typing import Dict, List, Optional
import json

# LINE Messaging API Configuration
LINE_MESSAGING_API_BASE_URL = 'https://api.line.me/v2/bot'

def send_line_message(channel_access_token: str, user_id: str, message: str) -> Dict:
    """
    Send a text message to a LINE user
    
    Args:
        channel_access_token (str): LINE Channel Access Token
        user_id (str): LINE User ID to send message to
        message (str): Message text to send
        
    Returns:
        Dict: Response from LINE API
    """
    try:
        headers = {
            'Authorization': f'Bearer {channel_access_token}',
            'Content-Type': 'application/json'
        }
        
        payload = {
            'to': user_id,
            'messages': [
                {
                    'type': 'text',
                    'text': message
                }
            ]
        }
        
        response = requests.post(
            f"{LINE_MESSAGING_API_BASE_URL}/message/push",
            headers=headers,
            json=payload,
            timeout=30
        )
        
        if response.status_code == 200:
            return {'status': True, 'message': 'Message sent successfully'}
        else:
            return {
                'status': False, 
                'error': f"Failed to send message: {response.status_code} - {response.text}"
            }
            
    except Exception as e:
        return {'status': False, 'error': f"Error sending message: {str(e)}"}

def get_line_bot_info(channel_access_token: str) -> Dict:
    """
    Get LINE bot information
    
    Args:
        channel_access_token (str): LINE Channel Access Token
        
    Returns:
        Dict: Bot information
    """
    try:
        headers = {
            'Authorization': f'Bearer {channel_access_token}',
            'Content-Type': 'application/json'
        }
        
        response = requests.get(
            f"{LINE_MESSAGING_API_BASE_URL}/info",
            headers=headers,
            timeout=30
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            raise Exception(f"Bot info fetch failed: {response.status_code} - {response.text}")
            
    except Exception as e:
        raise Exception(f"Error fetching bot info: {str(e)}")

def get_line_user_profile_messaging(channel_access_token: str, user_id: str) -> Dict:
    """
    Get user profile through Messaging API
    
    Args:
        channel_access_token (str): LINE Channel Access Token
        user_id (str): LINE User ID
        
    Returns:
        Dict: User profile information
    """
    try:
        headers = {
            'Authorization': f'Bearer {channel_access_token}',
            'Content-Type': 'application/json'
        }
        
        response = requests.get(
            f"{LINE_MESSAGING_API_BASE_URL}/profile/{user_id}",
            headers=headers,
            timeout=30
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            raise Exception(f"User profile fetch failed: {response.status_code} - {response.text}")
            
    except Exception as e:
        raise Exception(f"Error fetching user profile: {str(e)}")

def get_line_followers(channel_access_token: str, continuation_token: Optional[str] = None) -> Dict:
    """
    Get list of users who have added the LINE bot as a friend
    
    Args:
        channel_access_token (str): LINE Channel Access Token
        continuation_token (str, optional): Token for pagination
        
    Returns:
        Dict: List of follower user IDs
    """
    try:
        headers = {
            'Authorization': f'Bearer {channel_access_token}',
            'Content-Type': 'application/json'
        }
        
        url = f"{LINE_MESSAGING_API_BASE_URL}/followers/ids"
        if continuation_token:
            url += f"?start={continuation_token}"
        
        response = requests.get(
            url,
            headers=headers,
            timeout=30
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            raise Exception(f"Followers fetch failed: {response.status_code} - {response.text}")
            
    except Exception as e:
        raise Exception(f"Error fetching followers: {str(e)}")

def handle_line_webhook(webhook_data: Dict) -> Dict:
    """
    Handle incoming LINE webhook events
    
    Args:
        webhook_data (Dict): Webhook payload from LINE
        
    Returns:
        Dict: Processing result
    """
    try:
        events = webhook_data.get('events', [])
        processed_events = []
        
        for event in events:
            event_type = event.get('type')
            user_id = event.get('source', {}).get('userId')
            
            if event_type == 'message':
                message_type = event.get('message', {}).get('type')
                message_text = event.get('message', {}).get('text', '')
                
                processed_event = {
                    'type': 'message',
                    'user_id': user_id,
                    'message_type': message_type,
                    'message_text': message_text,
                    'timestamp': event.get('timestamp'),
                    'reply_token': event.get('replyToken')
                }
                processed_events.append(processed_event)
                
            elif event_type == 'follow':
                processed_event = {
                    'type': 'follow',
                    'user_id': user_id,
                    'timestamp': event.get('timestamp'),
                    'reply_token': event.get('replyToken')
                }
                processed_events.append(processed_event)
                
            elif event_type == 'unfollow':
                processed_event = {
                    'type': 'unfollow',
                    'user_id': user_id,
                    'timestamp': event.get('timestamp')
                }
                processed_events.append(processed_event)
        
        return {
            'status': True,
            'processed_events': processed_events,
            'total_events': len(processed_events)
        }
        
    except Exception as e:
        return {'status': False, 'error': f"Error processing webhook: {str(e)}"}

def reply_line_message(channel_access_token: str, reply_token: str, message: str) -> Dict:
    """
    Reply to a LINE message using reply token
    
    Args:
        channel_access_token (str): LINE Channel Access Token
        reply_token (str): Reply token from webhook event
        message (str): Message text to reply
        
    Returns:
        Dict: Response from LINE API
    """
    try:
        headers = {
            'Authorization': f'Bearer {channel_access_token}',
            'Content-Type': 'application/json'
        }
        
        payload = {
            'replyToken': reply_token,
            'messages': [
                {
                    'type': 'text',
                    'text': message
                }
            ]
        }
        
        response = requests.post(
            f"{LINE_MESSAGING_API_BASE_URL}/message/reply",
            headers=headers,
            json=payload,
            timeout=30
        )
        
        if response.status_code == 200:
            return {'status': True, 'message': 'Reply sent successfully'}
        else:
            return {
                'status': False, 
                'error': f"Failed to send reply: {response.status_code} - {response.text}"
            }
            
    except Exception as e:
        return {'status': False, 'error': f"Error sending reply: {str(e)}"}

def get_line_message_quota(channel_access_token: str) -> Dict:
    """
    Get the current message quota usage
    
    Args:
        channel_access_token (str): LINE Channel Access Token
        
    Returns:
        Dict: Quota information
    """
    try:
        headers = {
            'Authorization': f'Bearer {channel_access_token}',
            'Content-Type': 'application/json'
        }
        
        response = requests.get(
            f"{LINE_MESSAGING_API_BASE_URL}/message/quota",
            headers=headers,
            timeout=30
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            raise Exception(f"Quota fetch failed: {response.status_code} - {response.text}")
            
    except Exception as e:
        raise Exception(f"Error fetching quota: {str(e)}")

def broadcast_line_message(channel_access_token: str, message: str) -> Dict:
    """
    Broadcast a message to all followers
    
    Args:
        channel_access_token (str): LINE Channel Access Token
        message (str): Message text to broadcast
        
    Returns:
        Dict: Response from LINE API
    """
    try:
        headers = {
            'Authorization': f'Bearer {channel_access_token}',
            'Content-Type': 'application/json'
        }
        
        payload = {
            'messages': [
                {
                    'type': 'text',
                    'text': message
                }
            ]
        }
        
        response = requests.post(
            f"{LINE_MESSAGING_API_BASE_URL}/message/broadcast",
            headers=headers,
            json=payload,
            timeout=30
        )
        
        if response.status_code == 200:
            return {'status': True, 'message': 'Broadcast sent successfully'}
        else:
            return {
                'status': False, 
                'error': f"Failed to broadcast message: {response.status_code} - {response.text}"
            }
            
    except Exception as e:
        return {'status': False, 'error': f"Error broadcasting message: {str(e)}"}


def get_line_user_conversations(access_token: str, page: int = 1, per_page: int = 100) -> Dict:
    """
    Get user's LINE conversations/chats
    Note: This is a conceptual implementation as LINE doesn't provide direct access to user's personal chats
    In practice, you would need to implement this through LINE Bot interactions

    Args:
        access_token (str): User's LINE access token
        page (int): Page number for pagination
        per_page (int): Number of conversations per page

    Returns:
        Dict: Conversation data or error message
    """
    try:
        # This is a placeholder implementation
        # In reality, LINE doesn't provide an API to access user's personal chat history
        # You would need to implement this through:
        # 1. LINE Bot that users interact with
        # 2. Store conversations in your database when users send messages to your bot
        # 3. Retrieve from your own database

        return {
            'status': False,
            'error': 'LINE does not provide access to user personal chat history. Use LINE Bot interactions instead.',
            'note': 'Implement conversation storage through LINE Bot webhook events',
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': 0,
                'has_next': False,
                'has_previous': False
            }
        }

    except Exception as e:
        return {
            'status': False,
            'error': f'Error getting LINE conversations: {str(e)}'
        }


def get_line_chat_messages(access_token: str, chat_id: Optional[str] = None, page: int = 1, per_page: int = 100) -> Dict:
    """
    Get messages from a specific LINE chat
    Note: This is conceptual - LINE doesn't provide access to personal chat history

    Args:
        access_token (str): User's LINE access token
        chat_id (str, optional): Specific chat ID
        page (int): Page number for pagination
        per_page (int): Number of messages per page

    Returns:
        Dict: Message data or error message
    """
    try:
        # Placeholder implementation
        # In practice, you would:
        # 1. Store messages received through LINE Bot webhook
        # 2. Retrieve from your database with pagination

        offset = (page - 1) * per_page

        # Example of how you might retrieve from your own database:
        # from Authentication.models import LineMessage
        # messages = LineMessage.objects.filter(
        #     user_id=user_id,
        #     chat_id=chat_id if chat_id else None
        # ).order_by('-timestamp')[offset:offset + per_page]

        return {
            'status': False,
            'error': 'LINE chat message retrieval requires custom implementation through LINE Bot',
            'suggestion': 'Store messages from LINE Bot webhook events in your database',
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': 0,
                'has_next': False,
                'has_previous': False
            }
        }

    except Exception as e:
        return {
            'status': False,
            'error': f'Error getting LINE chat messages: {str(e)}'
        }


def store_line_message(webhook_event: Dict, brand_id: str) -> Dict:
    """
    Store LINE message from webhook event to database
    This is how you would actually implement message storage

    Args:
        webhook_event (Dict): LINE webhook event data
        brand_id (str): Brand ID associated with the message

    Returns:
        Dict: Storage result
    """
    try:
        # from Authentication.models import LineMessage  # You would need to create this model

        if webhook_event.get('type') == 'message':
            message_data = {
                'brand_id': brand_id,
                'line_user_id': webhook_event.get('source', {}).get('userId'),
                'message_id': webhook_event.get('message', {}).get('id'),
                'message_type': webhook_event.get('message', {}).get('type'),
                'message_text': webhook_event.get('message', {}).get('text', ''),
                'timestamp': webhook_event.get('timestamp'),
                'reply_token': webhook_event.get('replyToken')
            }

            # Store in database
            # LineMessage.objects.create(**message_data)

            return {
                'status': True,
                'message': 'Message stored successfully',
                'data': message_data
            }

        return {
            'status': False,
            'error': 'Not a message event'
        }

    except Exception as e:
        return {
            'status': False,
            'error': f'Error storing LINE message: {str(e)}'
        }


def get_stored_line_messages(brand_id: str, line_user_id: Optional[str] = None, page: int = 1, per_page: int = 100) -> Dict:
    """
    Retrieve stored LINE messages from database with pagination

    Args:
        brand_id (str): Brand ID to filter messages
        line_user_id (str, optional): Specific LINE user ID
        page (int): Page number for pagination
        per_page (int): Number of messages per page

    Returns:
        Dict: Retrieved messages with pagination info
    """
    try:
        # from Authentication.models import LineMessage  # You would need to create this model
        # from django.core.paginator import Paginator

        # Filter messages
        # queryset = LineMessage.objects.filter(brand_id=brand_id)
        # if line_user_id:
        #     queryset = queryset.filter(line_user_id=line_user_id)
        #
        # queryset = queryset.order_by('-timestamp')
        #
        # paginator = Paginator(queryset, per_page)
        # page_obj = paginator.get_page(page)
        #
        # messages = []
        # for msg in page_obj:
        #     messages.append({
        #         'id': msg.id,
        #         'line_user_id': msg.line_user_id,
        #         'message_text': msg.message_text,
        #         'message_type': msg.message_type,
        #         'timestamp': msg.timestamp,
        #         'created_at': msg.created_at
        #     })

        return {
            'status': False,
            'error': 'LineMessage model not implemented yet',
            'note': 'Create LineMessage model in Authentication/models.py to store messages',
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': 0,
                'has_next': False,
                'has_previous': False
            }
        }

    except Exception as e:
        return {
            'status': False,
            'error': f'Error retrieving stored LINE messages: {str(e)}'
        }
