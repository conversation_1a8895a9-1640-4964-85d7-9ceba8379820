from django.urls import path

from X.x_auth import *
from .views import *

urlpatterns = [
    path('register/', UserRegisterView.as_view()),
    path('industry/', UserIndustryView.as_view()),
    path('switch-user/', UserTypeSwitchView.as_view()),
    path('profile/', GetUserProfileView.as_view()),
    path('user-admin-profile/', GetUserProfileAdminView.as_view()),
    path('user-posts/', GetUserPostsView.as_view()),
    path('user-text-posts/', GetUserTextPostsView.as_view()),
    path('edit-profile/', EditUserProfileView.as_view()),
    path('delete-profile/', DeleteAccountView.as_view()),
    path('blocked-users/', BlockedUsersListView.as_view()),
    path('block-user/', BlockUserView.as_view()),
    path('login/', UserLoginView.as_view()),
    path('graph-admin/', GraphApi.as_view()),
    path('forgot-password/', UserForgotPasswordView.as_view()),
    path('verify-otp/', UserVerifyOtpView.as_view()),
    path('reset-password/', UserNewPasswordView.as_view()),
    path('get-thirdparty/', GetThirdParty.as_view()),
    path('user-status/', CheckUserStatusView.as_view()),
    path('logout/', LogoutView.as_view()),

    #Switch User
    path('switch-user-account/', SwitchUserView.as_view()),

    # Youtube
    path('youtube-url/', YoutubeAuthUrlView.as_view()),
    path('youtube/', YoutubeAuthView.as_view()),
    path('disconnect-youtube/', UserYoutubeDisconnectView.as_view()),
    # Linkedin
    path('linkedin/', LinkedInAuthView.as_view()),
    path('linkedin-url/', LinkedInUrlView.as_view()),
    path('disconnect-linkedin/', UserLinkedinDisconnectView.as_view()),
    # Pinterest
    path('pinterest/', PinterestAuthView.as_view()),
    path('pinterest-url/', PinterestUrlView.as_view()),
    path('disconnect-pinterest/', UserPinterestDisconnectView.as_view()),
    # Vimeo
    path('vimeo-url/', VimeoUrlView.as_view()),
    path('vimeo-login/', VimeoAuthView.as_view()),
    path('disconnect-vimeo/', UserVimeoDisconnectView.as_view()),
    # Instagram
    path('instagram-url/', InstagramUrlView.as_view()),
    path('instagram/', InstagramCallbackView.as_view()),
    path('disconnect-instagram/', UserInstagramDisconnectView.as_view()),
    # Thread
    path('thread-url/', ThreadUrlView.as_view()),
    path('threads/', ThreadAuthView.as_view()),
    path('disconnect-thread/', UserThreadDisconnectView.as_view()),

    # facebook
    path('facebook-url/', FacebookAuthUrl.as_view()),
    path('facebook/', FaceboookCallbackView.as_view()),
    path('facebook-after-auth/', FacebookAfterAuth.as_view()),
    path('disconnect-facebook/', UserFacebookDisconnectView.as_view()),
    # TikTok
    path('tiktok-url/', TikTokUrlView.as_view()),
    path('tiktok-auth/', TikTokAuthView.as_view()),
    path('tiktok-after-auth/', TikTokAfterAuthView.as_view()),
    path('disconnect-tiktok/', UserTiktokDisconnectView.as_view()),

    # tumblr
    path('tumblr/', GetAuthorizationURL.as_view()),
    path('tumblr-auth/', GetTumblrRedirect.as_view()),
    path('tumblr-after-auth/', GetAccessToken.as_view()),
    path('disconnect-tumblr/', UsertumblrDisconnectView.as_view()),

    # Reddit
    path('reddit-auth/', RedditAuthorizationUrlView.as_view()),
    path('reddit/', RedditRedirectView.as_view()),
    path('reddit-after-auth/', RedditAfterAuthView.as_view()),
    path('disconnect-reddit/', UserRedditDisconnectView.as_view()),

    # Bluesky
    path('bluesky/', BlueskyUrlView.as_view()),

    #Twitter
    path('twitter-login/', TwitterLoginView.as_view()),
    path('twitter-callback/', TwitterCallbackView.as_view()),
    path('disconnect-twitter/', DisconnectXView.as_view()),
    path('twitter-refresh-token/', TwitterRefreshTokenView.as_view()),

    # LINE
    path('line-url/', LineAuthUrlView.as_view()),
    path('line-callback/', LineCallbackView.as_view()),
    path('line-after-auth/', LineAfterAuthView.as_view()),
    path('disconnect-line/', UserLineDisconnectView.as_view()),
    path('line-messaging/', LineMessagingView.as_view()),
    path('line-chats/', LineChatsView.as_view()),
    path('line-webhook/', line_webhook_handler),

    # LINE Module Channel (Corporate Feature)
    path('line-module-auth/', LineModuleChannelAuthView.as_view()),
    path('line-module-callback/', LineModuleChannelCallbackView.as_view()),

    # Brands
    path('register-brand/', RegisterBrand.as_view()),
    path('edit-brand/', EditBrandView.as_view()),
    path('edit-brand-web/', EditBrandWebView.as_view()),
    path('delete-brand/', DeleteBrandView.as_view()),
    path('get-brands/', GetBrandDetails.as_view()),
    path('get-brands-web/', GetBrandWebDetails.as_view()),
    path('select-brand/', SelectBrandView.as_view()),
    path('user-data-home/', HomeScreenApi.as_view()),
    path('current-brand/', CurrentBrandView.as_view()),

    #Survey 
    path('survey/', SurveyForms.as_view()),
    path('update-subscription/', UpdateSubscriptionStatusView.as_view()),

    #subscription
    path('create-subscription/', CreateSubscriptionView.as_view()),
    path('subscribe/', UserSubscribeView.as_view()),
    # path('check-subscribe/', GetSubscriptionStatusView.as_view()),

    #User Management
    path('user-management/', UserManagementView.as_view()),
    path('get-invited-users/', GetInvitedUsersView.as_view()),
    path('get-invitee-users/', GetInviteeUsersView.as_view()),
    path('user-roles/', CresteUserRoles.as_view()),
    path('get-pending-invites/', GetPendingInviteUsersView.as_view()),
    path('approve-invite/', ApproveInviteUserView.as_view()),
    path('decline-invite/', DeclineInviteView.as_view()),
    path('revoke-invite/', RevokeInviteView.as_view()),
    path('role-data/', UserDataGetView.as_view()),

    #Industry Type
    path('create-industry/', CreateIndustryView.as_view()),
    path('create-user-type/', CreateUserTypeView.as_view()),
    path('get-industry/', GetIndustryListView.as_view()),

    #Beta Tester
    path('register-beta-user/',RegisterBetaTesterView.as_view()),

    #Version
    path('app-version/',VersionControlView.as_view()),

    #Purchase X
    path('purchase-x/', PurchaseXView.as_view()),
    path('payment/callback/',RazorpayCallbackView,name='razorpay-callback'),
]
