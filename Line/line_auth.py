import requests
import secrets
import hashlib
import base64
import urllib.parse
from typing import Dict, Optional, Tuple

# LINE OAuth 2.0 Configuration
# ⚠️ IMPORTANT: Replace these with your actual LINE Channel credentials
# Create a new LINE Channel at: https://developers.line.biz/console/
# 1. Create Provider → Create Channel → LINE Login
# 2. Copy Channel ID and Channel Secret from Basic Settings
# 3. Add Callback URL in LINE Login settings
CLIENT_ID = 'YOUR_ACTUAL_CHANNEL_ID'  # Replace with real Channel ID from LINE Console
CLIENT_SECRET = 'YOUR_ACTUAL_CHANNEL_SECRET'  # Replace with real Channel Secret
# REDIRECT_URI = 'https://api.flowkar.com/api/line-callback/'
REDIRECT_URI = 'https://suitable-arguably-bird.ngrok-free.app/api/line-callback/'  # For development

# LINE API Endpoints
LINE_OAUTH_BASE_URL = 'https://access.line.me/oauth2/v2.1'
LINE_API_BASE_URL = 'https://api.line.me/v2'
LINE_MESSAGING_API_BASE_URL = 'https://api.line.me/v2/bot'

def generate_line_auth_url(brand_id: str) -> str:
    """
    Generate LINE OAuth 2.0 authorization URL with PKCE
    
    Args:
        brand_id (str): Brand ID to be used as state parameter
        
    Returns:
        str: Authorization URL
    """
    print(f"=== Generating LINE Auth URL ===")
    print(f"CLIENT_ID: {CLIENT_ID}")
    print(f"REDIRECT_URI: {REDIRECT_URI}")
    print(f"Brand ID: {brand_id}")

    # Generate code verifier and challenge for PKCE
    code_verifier = base64.urlsafe_b64encode(secrets.token_bytes(32)).decode('utf-8').rstrip('=')
    code_challenge = base64.urlsafe_b64encode(
        hashlib.sha256(code_verifier.encode('utf-8')).digest()
    ).decode('utf-8').rstrip('=')

    print(f"Code Verifier: {code_verifier}")
    print(f"Code Challenge: {code_challenge}")

    # Store code_verifier temporarily (in production, use Redis or database)
    # For now, we'll include it in the state parameter (not recommended for production)
    state = f"{brand_id}:{code_verifier}"

    # Try with minimal scopes first
    params = {
        'response_type': 'code',
        'client_id': CLIENT_ID,
        'redirect_uri': REDIRECT_URI,
        'state': state,
        'scope': 'profile',  # Only profile scope for testing
        'code_challenge': code_challenge,
        'code_challenge_method': 'S256'
        # Removed nonce for testing
    }

    auth_url = f"{LINE_OAUTH_BASE_URL}/authorize?" + urllib.parse.urlencode(params)
    print(f"Generated URL: {auth_url}")
    print(f"=== LINE Auth URL Generated ===")

    return auth_url

def exchange_code_for_token(code: str, state: str) -> Dict:
    """
    Exchange authorization code for access token
    
    Args:
        code (str): Authorization code from LINE
        state (str): State parameter containing brand_id and code_verifier
        
    Returns:
        Dict: Token response containing access_token, refresh_token, etc.
    """
    try:
        # Extract brand_id and code_verifier from state
        brand_id, code_verifier = state.split(':', 1)
        
        token_data = {
            'grant_type': 'authorization_code',
            'code': code,
            'redirect_uri': REDIRECT_URI,
            'client_id': CLIENT_ID,
            'client_secret': CLIENT_SECRET,
            'code_verifier': code_verifier
        }
        
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded'
        }
        
        response = requests.post(
            f"{LINE_OAUTH_BASE_URL}/token",
            data=token_data,
            headers=headers,
            timeout=30
        )
        
        if response.status_code == 200:
            token_info = response.json()
            token_info['brand_id'] = brand_id
            return token_info
        else:
            raise Exception(f"Token exchange failed: {response.status_code} - {response.text}")
            
    except Exception as e:
        raise Exception(f"Error exchanging code for token: {str(e)}")

def get_line_user_profile(access_token: str) -> Dict:
    """
    Get LINE user profile information
    
    Args:
        access_token (str): LINE access token
        
    Returns:
        Dict: User profile information
    """
    try:
        headers = {
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json'
        }
        
        response = requests.get(
            f"{LINE_API_BASE_URL}/profile",
            headers=headers,
            timeout=30
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            raise Exception(f"Profile fetch failed: {response.status_code} - {response.text}")
            
    except Exception as e:
        raise Exception(f"Error fetching user profile: {str(e)}")

def verify_line_token(access_token: str) -> Dict:
    """
    Verify LINE access token validity
    
    Args:
        access_token (str): LINE access token
        
    Returns:
        Dict: Token verification information
    """
    try:
        params = {
            'access_token': access_token
        }
        
        response = requests.get(
            f"{LINE_API_BASE_URL}/oauth/verify",
            params=params,
            timeout=30
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            raise Exception(f"Token verification failed: {response.status_code} - {response.text}")
            
    except Exception as e:
        raise Exception(f"Error verifying token: {str(e)}")

def refresh_line_token(refresh_token: str) -> Dict:
    """
    Refresh LINE access token using refresh token
    
    Args:
        refresh_token (str): LINE refresh token
        
    Returns:
        Dict: New token information
    """
    try:
        token_data = {
            'grant_type': 'refresh_token',
            'refresh_token': refresh_token,
            'client_id': CLIENT_ID,
            'client_secret': CLIENT_SECRET
        }
        
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded'
        }
        
        response = requests.post(
            f"{LINE_OAUTH_BASE_URL}/token",
            data=token_data,
            headers=headers,
            timeout=30
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            raise Exception(f"Token refresh failed: {response.status_code} - {response.text}")
            
    except Exception as e:
        raise Exception(f"Error refreshing token: {str(e)}")

def revoke_line_token(access_token: str) -> bool:
    """
    Revoke LINE access token (logout)
    
    Args:
        access_token (str): LINE access token to revoke
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        token_data = {
            'access_token': access_token,
            'client_id': CLIENT_ID,
            'client_secret': CLIENT_SECRET
        }
        
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded'
        }
        
        response = requests.post(
            f"{LINE_OAUTH_BASE_URL}/revoke",
            data=token_data,
            headers=headers,
            timeout=30
        )
        
        return response.status_code == 200
        
    except Exception as e:
        print(f"Error revoking token: {str(e)}")
        return False

def get_line_friendship_status(access_token: str) -> Dict:
    """
    Get friendship status with the LINE official account
    
    Args:
        access_token (str): LINE access token
        
    Returns:
        Dict: Friendship status information
    """
    try:
        headers = {
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json'
        }
        
        response = requests.get(
            f"{LINE_API_BASE_URL}/friendship/status",
            headers=headers,
            timeout=30
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            raise Exception(f"Friendship status fetch failed: {response.status_code} - {response.text}")
            
    except Exception as e:
        raise Exception(f"Error fetching friendship status: {str(e)}")
