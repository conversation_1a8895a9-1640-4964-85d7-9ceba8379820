import requests
import secrets
import hashlib
import base64
import urllib.parse
from typing import Dict, Optional, Tuple

# LINE OAuth 2.0 Configuration
# ⚠️ IMPORTANT: Create your own LINE Channel at https://developers.line.biz/console/
# Current credentials may be invalid - replace with your actual Channel credentials
CLIENT_ID = '2007630314'  # Replace with your Channel ID
CLIENT_SECRET = '6b81e6bc52b9258d5d3d7f0b35034514'  # Replace with your Channel Secret
# REDIRECT_URI = 'https://api.flowkar.com/api/line-callback/'
REDIRECT_URI = 'https://suitable-arguably-bird.ngrok-free.app/api/line-callback/'  # For development

# LINE API Endpoints
LINE_OAUTH_BASE_URL = 'https://access.line.me/oauth2/v2.1'  # For LINE Login
LINE_API_BASE_URL = 'https://api.line.me/v2'
LINE_MESSAGING_API_BASE_URL = 'https://api.line.me/v2/bot'

# LINE Module Channel OAuth (Corporate Feature)
LINE_MODULE_OAUTH_BASE_URL = 'https://manager.line.biz/module/auth/v1'
LINE_MODULE_API_BASE_URL = 'https://api.line.me/v2/bot'

# Module Channel Configuration (Corporate customers only)
MODULE_CHANNEL_ID = 'your_module_channel_id'  # Different from LOGIN Channel ID
MODULE_CHANNEL_SECRET = 'your_module_channel_secret'
MODULE_REDIRECT_URI = 'https://suitable-arguably-bird.ngrok-free.app/api/line-module-callback/'

def generate_line_auth_url(brand_id: str) -> str:
    """
    Generate LINE OAuth 2.0 authorization URL with PKCE
    
    Args:
        brand_id (str): Brand ID to be used as state parameter
        
    Returns:
        str: Authorization URL
    """
    # Generate code verifier and challenge for PKCE
    code_verifier = base64.urlsafe_b64encode(secrets.token_bytes(32)).decode('utf-8').rstrip('=')
    code_challenge = base64.urlsafe_b64encode(
        hashlib.sha256(code_verifier.encode('utf-8')).digest()
    ).decode('utf-8').rstrip('=')
    
    # Store code_verifier temporarily (in production, use Redis or database)
    # For now, we'll include it in the state parameter (not recommended for production)
    state = f"{brand_id}:{code_verifier}"
    
    params = {
        'response_type': 'code',
        'client_id': CLIENT_ID,
        'redirect_uri': REDIRECT_URI,
        'state': state,
        'scope': 'profile openid email',  # Basic scopes for user info
        'code_challenge': code_challenge,
        'code_challenge_method': 'S256',
        'nonce': secrets.token_urlsafe(16)
    }
    
    auth_url = f"{LINE_OAUTH_BASE_URL}/authorize?" + urllib.parse.urlencode(params)
    return auth_url

def exchange_code_for_token(code: str, state: str) -> Dict:
    """
    Exchange authorization code for access token
    
    Args:
        code (str): Authorization code from LINE
        state (str): State parameter containing brand_id and code_verifier
        
    Returns:
        Dict: Token response containing access_token, refresh_token, etc.
    """
    try:
        # Extract brand_id and code_verifier from state
        brand_id, code_verifier = state.split(':', 1)
        
        token_data = {
            'grant_type': 'authorization_code',
            'code': code,
            'redirect_uri': REDIRECT_URI,
            'client_id': CLIENT_ID,
            'client_secret': CLIENT_SECRET,
            'code_verifier': code_verifier
        }
        
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded'
        }
        
        response = requests.post(
            f"{LINE_OAUTH_BASE_URL}/token",
            data=token_data,
            headers=headers,
            timeout=30
        )
        
        if response.status_code == 200:
            token_info = response.json()
            token_info['brand_id'] = brand_id
            return token_info
        else:
            raise Exception(f"Token exchange failed: {response.status_code} - {response.text}")
            
    except Exception as e:
        raise Exception(f"Error exchanging code for token: {str(e)}")

def get_line_user_profile(access_token: str) -> Dict:
    """
    Get LINE user profile information
    
    Args:
        access_token (str): LINE access token
        
    Returns:
        Dict: User profile information
    """
    try:
        headers = {
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json'
        }
        
        response = requests.get(
            f"{LINE_API_BASE_URL}/profile",
            headers=headers,
            timeout=30
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            raise Exception(f"Profile fetch failed: {response.status_code} - {response.text}")
            
    except Exception as e:
        raise Exception(f"Error fetching user profile: {str(e)}")

def verify_line_token(access_token: str) -> Dict:
    """
    Verify LINE access token validity
    
    Args:
        access_token (str): LINE access token
        
    Returns:
        Dict: Token verification information
    """
    try:
        params = {
            'access_token': access_token
        }
        
        response = requests.get(
            f"{LINE_API_BASE_URL}/oauth/verify",
            params=params,
            timeout=30
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            raise Exception(f"Token verification failed: {response.status_code} - {response.text}")
            
    except Exception as e:
        raise Exception(f"Error verifying token: {str(e)}")

def refresh_line_token(refresh_token: str) -> Dict:
    """
    Refresh LINE access token using refresh token
    
    Args:
        refresh_token (str): LINE refresh token
        
    Returns:
        Dict: New token information
    """
    try:
        token_data = {
            'grant_type': 'refresh_token',
            'refresh_token': refresh_token,
            'client_id': CLIENT_ID,
            'client_secret': CLIENT_SECRET
        }
        
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded'
        }
        
        response = requests.post(
            f"{LINE_OAUTH_BASE_URL}/token",
            data=token_data,
            headers=headers,
            timeout=30
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            raise Exception(f"Token refresh failed: {response.status_code} - {response.text}")
            
    except Exception as e:
        raise Exception(f"Error refreshing token: {str(e)}")

def revoke_line_token(access_token: str) -> bool:
    """
    Revoke LINE access token (logout)
    
    Args:
        access_token (str): LINE access token to revoke
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        token_data = {
            'access_token': access_token,
            'client_id': CLIENT_ID,
            'client_secret': CLIENT_SECRET
        }
        
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded'
        }
        
        response = requests.post(
            f"{LINE_OAUTH_BASE_URL}/revoke",
            data=token_data,
            headers=headers,
            timeout=30
        )
        
        return response.status_code == 200
        
    except Exception as e:
        print(f"Error revoking token: {str(e)}")
        return False

def get_line_friendship_status(access_token: str) -> Dict:
    """
    Get friendship status with the LINE official account
    
    Args:
        access_token (str): LINE access token
        
    Returns:
        Dict: Friendship status information
    """
    try:
        headers = {
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json'
        }
        
        response = requests.get(
            f"{LINE_API_BASE_URL}/friendship/status",
            headers=headers,
            timeout=30
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            raise Exception(f"Friendship status fetch failed: {response.status_code} - {response.text}")
            
    except Exception as e:
        raise Exception(f"Error fetching friendship status: {str(e)}")


# LINE Module Channel Functions (Corporate Feature)
def generate_module_channel_auth_url(state: str, basic_search_id: str = None) -> str:
    """
    Generate LINE Module Channel OAuth 2.0 authorization URL

    ⚠️ CORPORATE FEATURE: Requires corporate application and approval

    Args:
        state (str): CSRF protection token
        basic_search_id (str, optional): LINE Official Account basic ID

    Returns:
        str: Module Channel authorization URL
    """
    # Module Channel configuration (needs to be set after corporate approval)
    MODULE_CHANNEL_ID = 'your_module_channel_id'  # Different from LOGIN Channel ID
    MODULE_REDIRECT_URI = 'https://suitable-arguably-bird.ngrok-free.app/api/line-module-callback/'
    LINE_MODULE_OAUTH_BASE_URL = 'https://manager.line.biz/module/auth/v1'

    params = {
        'response_type': 'code',
        'client_id': MODULE_CHANNEL_ID,
        'redirect_uri': MODULE_REDIRECT_URI,
        'scope': 'message:send message:receive profile:read',  # Bot messaging scopes
        'state': state,
        'region': 'JP'  # or 'TW'
    }

    if basic_search_id:
        params['basic_search_id'] = basic_search_id

    auth_url = f"{LINE_MODULE_OAUTH_BASE_URL}/authorize?" + urllib.parse.urlencode(params)
    return auth_url


def attach_module_channel(authorization_code: str, state: str) -> Dict:
    """
    Attach Module Channel to LINE Official Account

    ⚠️ CORPORATE FEATURE: Requires corporate application

    Args:
        authorization_code (str): Authorization code from Module Channel OAuth
        state (str): State parameter for CSRF protection

    Returns:
        Dict: Attachment result
    """
    try:
        # This would require corporate API access
        # Implementation depends on corporate partnership with LINE
        print(f"Module Channel attachment attempted with code: {authorization_code[:10]}...")
        print(f"State: {state}")

        return {
            'status': False,
            'error': 'Module Channel attachment requires corporate partnership with LINE',
            'requirement': 'Contact LINE sales representative for corporate access',
            'documentation': 'https://developers.line.biz/en/docs/partner-docs/',
            'note': 'This feature is only available to approved corporate customers'
        }

    except Exception as e:
        return {
            'status': False,
            'error': f'Error attaching module channel: {str(e)}'
        }
